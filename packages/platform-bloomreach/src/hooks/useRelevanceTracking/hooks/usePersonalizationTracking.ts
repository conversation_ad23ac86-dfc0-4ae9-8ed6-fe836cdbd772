import {
	type PersonalizationTrackingData,
	type PersonalizedComponent,
	usePersonalizationTrackingData,
} from "@anwb/platform-support";
import { useCallback } from "react";

import type { VariantCharacteristic } from "../../../types";
import type { TrackingEvent } from "../types";

import { trackDefaultEvent } from "../helpers/trackDefaultEvent";
import { trackFallbackEvent } from "../helpers/trackFallbackEvent";
import { transformComponentName } from "../helpers/transformComponentName";
import { useBlueConicDialogTracking } from "./useBlueConicDialogTracking";
import { useOfferTracking } from "./useOfferTracking";

type Params = {
	characteristics?: Array<VariantCharacteristic>;
	component: string;
};

// Define a new type for the hook's return function
type PersonalizationTrackingHandler = (
	event: TrackingEvent,
	position: number,
) => void;

export const usePersonalizationTracking = ({
	characteristics,
	component,
}: Params): PersonalizationTrackingHandler => {
	const findPersonalizedComponentForPosition = useCallback(
		(
			position: number,
			personalizedComponents: Array<PersonalizedComponent>,
		) => {
			return personalizedComponents.find(
				(personalizedComponent) => personalizedComponent.position === position,
			);
		},
		[],
	);

	const personalizationTrackingData: null | PersonalizationTrackingData =
		usePersonalizationTrackingData();

	const handleBlueConicDialogTracking = useBlueConicDialogTracking({
		component,
	});
	const handleOfferTracking = useOfferTracking({
		component,
	});
	const handleDefaultTracking = trackDefaultEvent(component);
	const handleFallbackTracking = trackFallbackEvent(component);

	return useCallback<PersonalizationTrackingHandler>(
		(event, position) => {
			// Transform the component name so that it can be matched against personalizationTrackingData
			const transformedComponentName =
				transformComponentName(component).toLowerCase();

			// Find the single characteristic for the provided position
			const characteristic = (characteristics ?? []).find(
				(characteristic) => characteristic.position === position,
			);

			if (!characteristic) {
				console.warn(
					`[Sombrero] Clicked position ${position} not found in characteristics for component ${component}. No tracking event sent.`,
				);
				return;
			}

			// Get the personalized component data for this type of component
			// IMPORTANT: This will not work properly if the page has multiple personalized components of the same type
			const personalizedComponents =
				personalizationTrackingData?.personalizedComponents?.filter(
					(item: PersonalizedComponent) =>
						item.componentName.toLowerCase() === transformedComponentName,
				);
			console.log(personalizedComponents, characteristic);
			if (personalizedComponents?.length === 0) {
				console.warn(
					`[Sombrero] No personalized components found for ${component} and event ${event}. This might be an issue if personalization is expected.`,
				);
				return;
			}

			console.log(variant, component);

			// Track based on the variant type
			switch (variant) {
				case "DEFAULT":
					// DEFAULT variant or no characteristic
					handleDefaultTracking({
						event,
						position,
					});
					break;
				case "FALLBACK":
					handleFallbackTracking({
						event,
						position,
					});
					break;
				case "PERSONALIZED":
								// Find the corresponding personalized component for this position (for PERSONALIZED varian)
			const personalizedComponent = findPersonalizedComponentForPosition(
				characteristic.position,
				personalizedComponents ?? [],
			);
			const variant = characteristic.variant;
					if (personalizedComponent?.provider === "dgp") {
						handleOfferTracking(
							event,
							position,
							personalizedComponent.responseId ?? "",
						);
					}

					if (personalizedComponent?.provider === "blueconic_dialogs") {
						handleBlueConicDialogTracking(event, position);
					}

					// If something went wrong, track as default
					if (!personalizedComponent) {
						handleDefaultTracking({
							event,
							position,
						});
					}
					break;
			}
		},
		[
			personalizationTrackingData,
			component,
			handleBlueConicDialogTracking,
			handleOfferTracking,
			handleDefaultTracking,
			handleFallbackTracking,
			findPersonalizedComponentForPosition,
			characteristics,
		],
	);
};
