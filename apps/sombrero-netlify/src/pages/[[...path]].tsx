import type {
	GetServerSidePropsContext,
	InferGetServerSidePropsType,
} from "next";

import { PortalApplicationProvider } from "@anwb/platform-applications";
import {
	BrPageLayout,
	isNotFoundPage,
	isRedirectPage,
} from "@anwb/platform-bloomreach";
import {
	ApplicationProvider,
	executeLoaders,
	LoaderProvider,
	PageMetaProvider,
	PersonalizationTrackingDataProvider,
} from "@anwb/platform-support";
import { Expoints, IrisChatbot } from "@anwb/platform-third-parties";
import { LegacyGlobalTypography } from "@anwb/poncho/design-tokens/theme";
import { CookieBanner } from "@anwb/poncho/widgets/cookie-banner";
import { BrComponent, BrPage } from "@bloomreach/react-sdk";
import { initialize } from "@bloomreach/spa-sdk";
import axios from "axios";

import type { OrchestratorResponse } from "../utils/netlify/schemas/orchestratorSchema";

import { ScriptGuard } from "../components/ScriptGuard";
import {
	BlueConic,
	GoogleTagManager,
	Optimizely,
	Speedcurve,
} from "../components/ThirdParties";
import { APPLICATION_MAPPING } from "../constants/application-mapping";
import { CACHE_EXCLUDED_PATHS } from "../constants/cache-excluded-paths";
import { LAYOUT_MAPPING } from "../constants/layout-mapping";
import { LOADER_MAPPING } from "../constants/loader-mapping";
import { mapLayoutToComponents } from "../helpers/mapLayoutToComponents";
import { DefaultLayout } from "../layouts/DefaultLayout";
import { getBaseConfig } from "../utils/bloomreach";
import { getDocumentId } from "../utils/bloomreach/getDocumentId";
import { getLoadersForPage } from "../utils/bloomreach/getLoadersForPage";
import { createHttpClient } from "../utils/bloomreach/http";
import { getPersonalizationSearchParams } from "../utils/bloomreach/http/searchParams";
import {
	checkIfCachingIsEnabled,
	disableCaching,
	isNotIncludedInExcludeList,
	isRunningInProductionMode,
} from "../utils/cache";
import { handleNetlifyVaryCache } from "../utils/cache/handleNetlifyVaryCache";
import { isPersonalizedPage } from "../utils/cache/isPersonalizedPage";
import { getOrigin } from "../utils/getOrigin";
import { getHeaders } from "../utils/headers/getHeaders";
import { handlePersonalizationHeaders } from "../utils/headers/handlePersonalizationHeaders";
import { fetchOrchestrator } from "../utils/netlify/fetchOrchestrator";
import { prepassApplication } from "../utils/prepass";
import { shouldRenderLegacyStyles } from "../utils/shouldRenderLegacyStyles";

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const orchestratorResponse: OrchestratorResponse =
		await fetchOrchestrator(ctx);

	if (
		orchestratorResponse.hasAuthState ||
		ctx.resolvedUrl.startsWith("/preview")
	) {
		disableCaching(ctx);
	}

	if (orchestratorResponse.redirect) {
		disableCaching(ctx);
		return {
			redirect: {
				destination: orchestratorResponse.redirect,
				permanent: false,
			},
		};
	}

	const config = getBaseConfig(ctx.resolvedUrl);
	const httpClient = createHttpClient(ctx.req, orchestratorResponse);

	const personalizationParams = getPersonalizationSearchParams(
		orchestratorResponse.personalization,
	);
	// TODO: Temporarily log these, remove this later
	const requestDetails = {
		personalizationParams: Array.from(personalizationParams.entries()).reduce<
			Record<string, Array<string>>
		>((acc, [key, value]) => {
			acc[key] = acc[key] ?? [];
			acc[key].push(value);
			return acc;
		}, {}),
		url: config.endpoint,
	};

	// Initialize page with a request to Bloomreach (preflight = false)
	const page = await initialize({ ...config, httpClient });

	if (isNotFoundPage(page)) {
		ctx.res.statusCode = 404;
	}

	if (isRedirectPage(page)) {
		return {
			redirect: {
				destination: page.model.meta.redirect,
				permanent: false,
			},
		};
	}

	// Get and apply cache headers
	const applyCacheHeaders = checkIfCachingIsEnabled(
		isRunningInProductionMode(),
		isNotIncludedInExcludeList(ctx.resolvedUrl, CACHE_EXCLUDED_PATHS),
		!orchestratorResponse.hasAuthState,
	);

	const bloomreachCacheIdentifier = getDocumentId(page);
	const personalizedPage = isPersonalizedPage(page);

	handleNetlifyVaryCache(
		ctx,
		personalizedPage,
		orchestratorResponse.personalization,
	);

	getHeaders({
		applyCacheHeaders,
		bloomreachCacheIdentifier,
		personalizedPage,
	}).forEach((value, key) => {
		ctx.res.setHeader(key, value);
	});

	// Set personalization headers (used by cache headers)
	await handlePersonalizationHeaders(applyCacheHeaders)(
		ctx,
		orchestratorResponse.personalization,
	);

	const loaders = getLoadersForPage(page, LOADER_MAPPING);

	const loaderData = await executeLoaders(loaders)(ctx.req, ctx.res);

	const pageContextData = await prepassApplication({
		ctx,
		page,
		pageMeta: {
			headers: ctx.req.headers,
			url: ctx.req.url,
		},
	});

	// Populate personalisationTrackingData from orchestratorResponse
	const personalizationTrackingData = {
		channel: "ANWB.NL",
		customerId: orchestratorResponse.customerId?.relationNumber ?? null,
		isRecognized: !!orchestratorResponse.hasAuthState,
		personalizedComponents:
			"personalizedComponents" in orchestratorResponse.personalization
				? orchestratorResponse.personalization.personalizedComponents
				: null,
	};
	const layout = page.getComponent().getName();

	return {
		props: {
			config,
			layout,
			loaderData,
			orchestratorResponse,
			page: JSON.stringify(page),
			pageContextData,
			pageMeta: {
				origin: getOrigin(ctx.req),
				url: ctx.resolvedUrl,
			},
			personalizationTrackingData,
			requestDetails,
		},
	};
}

export default function DynamicPage({
	config,
	layout,
	loaderData,
	orchestratorResponse,
	page,
	pageContextData,
	pageMeta,
	personalizationTrackingData,
	requestDetails,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	// TODO: Remove this
	console.log("ORCHESTRATOR RESPONSE", orchestratorResponse);
	console.log("PREFLIGHT FALSE PAGE STATE REQUEST", requestDetails);
	console.log("PREFLIGHT FALSE PAGE STATE RESPONSE", JSON.parse(page));

	return (
		<PortalApplicationProvider {...pageContextData}>
			{shouldRenderLegacyStyles(pageContextData.applicationConfiguration) && (
				<LegacyGlobalTypography />
			)}

			<PageMetaProvider {...pageMeta}>
				<PersonalizationTrackingDataProvider data={personalizationTrackingData}>
					<BrPage
						configuration={{ ...config, httpClient: axios }}
						mapping={mapLayoutToComponents(layout)}
						page={JSON.parse(page)}
					>
						<ApplicationProvider
							data={pageContextData.applicationData}
							mapping={APPLICATION_MAPPING}
						>
							<LoaderProvider data={loaderData}>
								<BrPageLayout default={DefaultLayout} mapping={LAYOUT_MAPPING}>
									<main id="main-content">
										<BrComponent path="main" />
									</main>
									<ScriptGuard>
										<IrisChatbot />
										<BlueConic />
										<Expoints />
										<Speedcurve />
										<GoogleTagManager />
										<Optimizely />
										<CookieBanner />
									</ScriptGuard>
								</BrPageLayout>
							</LoaderProvider>
						</ApplicationProvider>
					</BrPage>
				</PersonalizationTrackingDataProvider>
			</PageMetaProvider>
		</PortalApplicationProvider>
	);
}
